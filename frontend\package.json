{"name": "saber", "version": "4.3.0", "scripts": {"dev": "vite --host", "prod": "vite --mode production", "build": "vite build --mode development", "build:prod": "vite build --mode production", "serve": "vite preview --host"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@saber/nf-design-base-elp": "^1.2.0", "@saber/nf-form-design-elp": "^1.3.2", "@saber/nf-form-elp": "^1.3.3", "@smallwei/avue": "^3.5.6", "animate.css": "^4.1.1", "avue-plugin-ueditor": "^1.0.3", "axios": "^0.21.1", "crypto-js": "^4.1.1", "dayjs": "^1.10.6", "dingtalk-jsapi": "^3.1.1", "element-plus": "^2.8.7", "highlight.js": "^11.9.0", "js-base64": "^3.7.4", "js-cookie": "^3.0.0", "js-md5": "^0.7.3", "nprogress": "^0.2.0", "pdfjs-dist": "^2.5.207", "pinia": "^3.0.3", "sm-crypto": "^0.3.13", "vconsole": "^3.15.1", "vue": "^3.4.27", "vue-i18n": "^9.1.9", "vue-router": "^4.3.2", "vue-stage-play": "^0.5.2", "vue3-clipboard": "^1.0.0", "vuex": "^4.1.0"}, "devDependencies": {"@stagewise-plugins/vue": "^0.5.0", "@stagewise/toolbar-vue": "^0.5.0", "@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.4.27", "prettier": "^2.8.7", "sass": "^1.77.2", "unplugin-auto-import": "^0.11.2", "vite": "^5.2.12", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend": "^0.4.0"}}